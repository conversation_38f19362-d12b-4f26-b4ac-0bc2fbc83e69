<?php
require_once('db.php');
require_once('network_security.php');
date_default_timezone_set('Asia/Thimphu');

// Check network access before proceeding
blockUnauthorizedAccess();

$message = '';
$error = '';

// Check if MAC address column already exists
function columnExists($conn, $table, $column) {
    $result = $conn->query("SHOW COLUMNS FROM `$table` LIKE '$column'");
    return $result->num_rows > 0;
}

// Add MAC address column if it doesn't exist
if (!columnExists($conn, 'staff', 'mac_address')) {
    $sql = "ALTER TABLE `staff` ADD COLUMN `mac_address` varchar(17) DEFAULT NULL COMMENT 'Device MAC address for unique identification'";
    
    if ($conn->query($sql)) {
        $message .= "✅ Added mac_address column to staff table successfully.<br>";
        
        // Add index for MAC address
        $index_sql = "ALTER TABLE `staff` ADD KEY `idx_staff_mac_address` (`mac_address`)";
        if ($conn->query($index_sql)) {
            $message .= "✅ Added index for mac_address column successfully.<br>";
        } else {
            $error .= "❌ Error adding index for mac_address column: " . $conn->error . "<br>";
        }
    } else {
        $error .= "❌ Error adding mac_address column: " . $conn->error . "<br>";
    }
} else {
    $message .= "ℹ️ MAC address column already exists in staff table.<br>";
}

// Check for users with conflicting device characteristics who need MAC addresses
$conflict_check_sql = "
    SELECT s1.id, s1.full_name, s1.phone_model, s1.mac_address,
           COUNT(*) as conflict_count
    FROM staff s1 
    JOIN staff s2 ON s1.phone_model = s2.phone_model 
    WHERE s1.phone_model IS NOT NULL 
    AND s1.phone_model != '' 
    AND s1.unique_id IS NOT NULL 
    AND s1.unique_id != ''
    AND s2.unique_id IS NOT NULL 
    AND s2.unique_id != ''
    GROUP BY s1.phone_model 
    HAVING COUNT(*) > 1
    ORDER BY s1.phone_model, s1.full_name
";

$conflict_result = $conn->query($conflict_check_sql);
$users_needing_mac = [];

if ($conflict_result && $conflict_result->num_rows > 0) {
    while ($row = $conflict_result->fetch_assoc()) {
        $users_needing_mac[] = $row;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add MAC Address Column - Database Migration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        
        .message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .conflict-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
        }
        
        .user-conflict {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #ff6b6b;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-safe {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Database Migration: MAC Address Support</h1>
            <p>Adding MAC address column for enhanced user identification</p>
        </div>

        <?php if ($message): ?>
            <div class="message success">
                <?= $message ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="message error">
                <?= $error ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($users_needing_mac)): ?>
            <div class="conflict-section">
                <h3>⚠️ Users with Device Conflicts Detected</h3>
                <p>The following users have identical device characteristics and would benefit from MAC address registration:</p>
                
                <?php 
                $grouped_conflicts = [];
                foreach ($users_needing_mac as $user) {
                    $grouped_conflicts[$user['phone_model']][] = $user;
                }
                ?>
                
                <?php foreach ($grouped_conflicts as $phone_model => $users): ?>
                    <div class="user-conflict">
                        <strong>Device Type:</strong> <?= htmlspecialchars($phone_model) ?><br>
                        <strong>Conflicting Users:</strong>
                        <ul>
                            <?php foreach ($users as $user): ?>
                                <li>
                                    <?= htmlspecialchars($user['full_name']) ?>
                                    <?php if ($user['mac_address']): ?>
                                        <span style="color: green;">✅ MAC: <?= htmlspecialchars($user['mac_address']) ?></span>
                                    <?php else: ?>
                                        <span style="color: red;">❌ No MAC address</span>
                                    <?php endif; ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endforeach; ?>
                
                <div class="instructions">
                    <h3>📋 Next Steps:</h3>
                    <ol>
                        <li>Direct affected users to the <strong>Upgrade Registration</strong> page</li>
                        <li>Users should enter their device MAC address during registration</li>
                        <li>This will resolve identification conflicts between identical devices</li>
                        <li>Users can find their MAC address in device network settings</li>
                    </ol>
                </div>
            </div>
        <?php else: ?>
            <div class="message info">
                ℹ️ No device conflicts detected. All users have unique device characteristics.
            </div>
        <?php endif; ?>

        <div style="text-align: center; margin-top: 30px;">
            <a href="upgrade_registration.php" class="btn">🔄 Upgrade Registration</a>
            <a href="attendance.php" class="btn btn-safe">📋 Back to Attendance</a>
            <a href="admin_dashboard.php" class="btn btn-safe">🏠 Admin Dashboard</a>
        </div>

        <div style="margin-top: 20px; font-size: 14px; color: #666; text-align: center;">
            <p><strong>Migration completed successfully!</strong></p>
            <p>The staff table now supports MAC address-based user identification.</p>
        </div>
    </div>
</body>
</html>
