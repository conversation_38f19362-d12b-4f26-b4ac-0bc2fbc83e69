<?php
session_start();
require_once('db.php');
require_once('network_security.php');
date_default_timezone_set('Asia/Thimphu');

// Check network access before proceeding
blockUnauthorizedAccess();

$message = '';
$qr_generated = false;

// Handle admin login
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'login') {
        $admin_name = $_POST['admin_name'] ?? '';
        $admin_code = $_POST['admin_code'] ?? '';
        
        // Simple admin authentication
        $valid_admins = [
            'Choki Wangmo' => 'ADMIN2024',
            'Sonam Tenzin Yuedsel' => 'ADMIN2024'
        ];
        
        if (isset($valid_admins[$admin_name]) && $valid_admins[$admin_name] === $admin_code) {
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_name'] = $admin_name;
            $_SESSION['admin_login_time'] = time();

            // Redirect to homepage
            header("Location: index.php");
            exit();
        } else {
            $message = "❌ Invalid admin credentials. Please check your name and code.";
        }
    } elseif ($action === 'generate_qr') {
        // Generate admin login QR code
        require_once('phpqrcode/qrlib.php');
        
        $qr_data = "https://172.31.19.253/attendance/admin_login.php?auto_login=1";
        
        // Create qrcodes directory if it doesn't exist
        if (!file_exists('qrcodes')) {
            mkdir('qrcodes', 0777, true);
        }
        
        $qr_file = 'qrcodes/admin_login_qr.png';
        QRcode::png($qr_data, $qr_file, QR_ECLEVEL_H, 10);
        
        $message = "✅ Admin login QR code generated! Scan this QR code to access admin login page.";
        $qr_generated = true;
    } elseif ($action === 'logout') {
        session_destroy();
        $message = "✅ Successfully logged out. You can now login again or return to the main page.";
    }
}

// Handle auto-login from QR code
if (isset($_GET['auto_login']) && $_GET['auto_login'] == '1') {
    $message = "📱 QR Code scanned! Please select your admin name and enter the admin code below.";
}

// Handle logout from URL parameter (when clicking logout links)
if (isset($_GET['logout']) && $_GET['logout'] == '1') {
    session_destroy();
    session_start(); // Restart session for message
    $message = "✅ Successfully logged out. You can now login again or return to the main page.";
}

// Check if already logged in
$is_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Attendance System</title>
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 500px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            position: relative;
        }
        .home-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .home-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .form-group {
            margin-bottom: 25px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 0.95rem;
        }
        select, input, button {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        select:focus, input:focus {
            outline: none;
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
        }
        button {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            margin-top: 10px;
            transition: transform 0.2s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        .qr-btn {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        .logout-btn {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        }
        .message {
            padding: 20px;
            margin: 25px 0;
            border-radius: 10px;
            font-weight: 500;
            line-height: 1.5;
        }
        .success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-left: 4px solid #28a745;
        }
        .error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        .info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }
        .qr-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            text-align: center;
            border: 2px solid #dee2e6;
        }
        .qr-display img {
            max-width: 250px;
            width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .admin-panel {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 6px solid #28a745;
        }
        .nav-links {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
        }
        .nav-links a {
            display: inline-block;
            margin: 5px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            body {
                padding: 5px;
            }
            .container {
                margin: 20px auto;
            }
            .header {
                padding: 20px 15px;
            }
            .header h1 {
                font-size: 1.5rem;
            }
            .content {
                padding: 20px 15px;
            }
            .home-btn {
                position: static;
                display: block;
                text-align: center;
                margin: 0 auto 20px auto;
                max-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.php" class="home-btn">🏠 Home</a>
        <div class="header">
            <h1>👨‍💼 Admin Login</h1>
            <p>Secure access for authorized administrators</p>
        </div>
        
        <div class="content">
            <?php if ($message): ?>
                <div class="message <?= strpos($message, '✅') !== false ? 'success' : (strpos($message, '📱') !== false ? 'info' : 'error') ?>">
                    <?= $message ?>
                </div>
            <?php endif; ?>

            <?php if ($is_logged_in): ?>
                <!-- Admin Panel -->
                <div class="admin-panel">
                    <h3 style="margin-top: 0; color: #155724;">✅ Admin Access Granted</h3>
                    <p><strong>Welcome:</strong> <?= htmlspecialchars($_SESSION['admin_name']) ?></p>
                    <p><strong>Login Time:</strong> <?= date('Y-m-d H:i:s', $_SESSION['admin_login_time']) ?></p>
                    <p>You now have access to all admin features.</p>
                    
                    <form method="POST" style="margin-top: 20px;">
                        <input type="hidden" name="action" value="logout">
                        <button type="submit" class="logout-btn">🚪 Logout</button>
                    </form>
                </div>


            <?php else: ?>
                <!-- Login Form -->
                <form method="POST">
                    <div class="form-group">
                        <label for="admin_name">👤 Select Admin Name:</label>
                        <select name="admin_name" id="admin_name" required>
                            <option value="">-- Choose Admin --</option>
                            <option value="Choki Wangmo">Choki Wangmo</option>
                            <option value="Sonam Tenzin Yuedsel">Sonam Tenzin Yuedsel</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_code">🔐 Admin Code:</label>
                        <input type="password" name="admin_code" id="admin_code" placeholder="Enter admin code" required>
                    </div>
                    
                    <input type="hidden" name="action" value="login">
                    <button type="submit">🔓 Login as Admin</button>
                </form>




            <?php endif; ?>
        </div>
    </div>
</body>
</html>
