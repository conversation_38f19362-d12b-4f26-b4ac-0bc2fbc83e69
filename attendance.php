<?php
session_start();
require_once('network_security.php');
date_default_timezone_set('Asia/Thimphu');

// CRITICAL DEBUG: Confirm this is the NEW VERSION running
file_put_contents('debug_log.txt', "[" . date('Y-m-d H:i:s') . "] *** NEW FIXED VERSION OF ATTENDANCE.PHP IS RUNNING ***\n", FILE_APPEND);

// Check network access before proceeding
blockUnauthorizedAccess();

// Prevent caching with stronger headers
header("Cache-Control: no-cache, no-store, must-revalidate, max-age=0, private");
header("Pragma: no-cache");
header("Expires: 0");
header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
header("ETag: " . md5(time()));
header("Vary: *");

// Debug logging function
function debug_log($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents('debug_log.txt', "[$timestamp] $message\n", FILE_APPEND);
}

// Generate device characteristics hash (for matching against stored device info)
function generateDeviceCharacteristicsHash($deviceInfo) {
    // Create a hash from device characteristics only (no user-specific data)
    $fingerprintData = [
        'userAgent' => $deviceInfo['userAgent'] ?? '',
        'screenResolution' => $deviceInfo['screenResolution'] ?? '',
        'timezone' => $deviceInfo['timezone'] ?? '',
        'language' => $deviceInfo['language'] ?? '',
        'platform' => $deviceInfo['platform'] ?? '',
        'hardwareConcurrency' => $deviceInfo['hardwareConcurrency'] ?? '',
        'colorDepth' => $deviceInfo['colorDepth'] ?? '',
        'touchSupport' => $deviceInfo['touchSupport'] ?? '',
        'deviceMemory' => $deviceInfo['deviceMemory'] ?? '',
        'cookieEnabled' => $deviceInfo['cookieEnabled'] ?? '',
        'doNotTrack' => $deviceInfo['doNotTrack'] ?? '',
        'onLine' => $deviceInfo['onLine'] ?? ''
    ];

    // Sort to ensure consistent ordering
    ksort($fingerprintData);
    $fingerprintString = json_encode($fingerprintData);

    return hash('sha256', $fingerprintString);
}

// DB connection
$conn = new mysqli("localhost", "root", "", "attendance_system");
if ($conn->connect_error) die("Connection failed: " . $conn->connect_error);

$today = date("Y-m-d");
$now = date("H:i:s");

// Get device information from POST only (no cookies for phone_model to avoid conflicts)
$phone_model = $_POST['phone_model'] ?? null;
$device_info_json = $_POST['device_info'] ?? null;
$device_characteristics_hash = null;
$device_token = $_COOKIE['device_token'] ?? null;

// Generate device characteristics hash if we have device info
if ($device_info_json) {
    $device_info = json_decode($device_info_json, true);
    if ($device_info) {
        $device_characteristics_hash = generateDeviceCharacteristicsHash($device_info);
        debug_log("attendance.php - Generated device characteristics hash: " . $device_characteristics_hash);
    }
}

// Clear any existing problematic cookies to prevent user conflicts on shared devices
if (isset($_COOKIE['phone_model'])) {
    setcookie('phone_model', '', time() - 3600, '/'); // Clear the cookie
    debug_log("attendance.php - Cleared phone_model cookie to prevent user conflicts");
}

// CRITICAL: Clear any old device_fingerprint cookies that might be causing the issue
if (isset($_COOKIE['device_fingerprint'])) {
    setcookie('device_fingerprint', '', time() - 3600, '/'); // Clear the cookie
    debug_log("attendance.php - Cleared old device_fingerprint cookie that was causing user conflicts");
}

// Check if user is admin (based on session)
$is_admin = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;

debug_log("attendance.php - NEW FIXED VERSION " . date('Y-m-d H:i:s') . " - Phone model: " . ($phone_model ?? 'null') . ", Device characteristics hash: " . ($device_characteristics_hash ?? 'null') . ", Device token: " . ($device_token ?? 'null') . ", Is admin: " . ($is_admin ? 'yes' : 'no'));
debug_log("attendance.php - NO MORE CACHED FINGERPRINTS - Using fresh device detection every time");

$staff = null;
$identification_method = 'none';
$conflicting_users = [];

// Enhanced staff lookup using multiple strategies (in order of reliability)
// Strategy 1: Use device token (most reliable for registered devices)
if ($device_token && !$staff) {
    $stmt = $conn->prepare("SELECT * FROM staff WHERE device_token = ? AND unique_id IS NOT NULL AND unique_id != ''");
    $stmt->bind_param("s", $device_token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 1) {
        $staff = $result->fetch_assoc();
        $identification_method = 'device_token';
        debug_log("attendance.php - Found unique staff by device token: " . $staff['full_name'] . " (ID: " . $staff['unique_id'] . ")");
    }
    $stmt->close();
}

// Strategy 2: Use device characteristics matching (compare against stored device_info)
if (!$staff && $device_characteristics_hash && $device_info_json) {
    // Get all registered users with device info
    $stmt = $conn->prepare("SELECT * FROM staff WHERE device_info IS NOT NULL AND device_info != '' AND unique_id IS NOT NULL AND unique_id != ''");
    $stmt->execute();
    $result = $stmt->get_result();

    $current_device_info = json_decode($device_info_json, true);
    $matching_users = [];

    while ($row = $result->fetch_assoc()) {
        $stored_device_info = json_decode($row['device_info'], true);
        if ($stored_device_info) {
            // Calculate similarity score based on key device characteristics
            $score = 0;
            $total_checks = 0;

            // Enhanced fingerprint matching with weighted scoring
            $fingerprint_characteristics = [
                'canvasFingerprint' => 25,      // Highest weight - most unique
                'webglFingerprint' => 20,       // Very unique
                'audioFingerprint' => 15,       // Unique
                'fontFingerprint' => 15,        // Unique
                'cpuFingerprint' => 10,         // Moderately unique
                'gpuInfo' => 10,                // Moderately unique
                'screenDetails' => 5            // Less unique but still useful
            ];

            $max_possible_score = array_sum($fingerprint_characteristics);

            foreach ($fingerprint_characteristics as $key => $weight) {
                $total_checks += $weight;
                if (isset($current_device_info[$key]) && isset($stored_device_info[$key])) {
                    if ($current_device_info[$key] === $stored_device_info[$key]) {
                        $score += $weight;
                    } elseif ($key === 'canvasFingerprint' || $key === 'webglFingerprint') {
                        // For canvas/webgl, check partial similarity
                        $similarity = similar_text($current_device_info[$key], $stored_device_info[$key]);
                        if ($similarity > 50) { // 50+ character similarity
                            $score += ($weight * 0.7); // Partial credit
                        }
                    }
                }
            }

            // Require at least 70% match on weighted characteristics (more lenient due to enhanced fingerprinting)
            $match_percentage = ($score / $total_checks) * 100;
            if ($match_percentage >= 70) {
                $matching_users[] = [
                    'user' => $row,
                    'score' => $score,
                    'percentage' => $match_percentage
                ];
            }
        }
    }

    if (count($matching_users) == 1) {
        // Only one matching user - safe to identify
        $staff = $matching_users[0]['user'];
        $identification_method = 'device_characteristics';
        debug_log("attendance.php - Found unique staff by device characteristics: " . $staff['full_name'] . " (ID: " . $staff['unique_id'] . ") with " . round($matching_users[0]['percentage']) . "% match");
    } elseif (count($matching_users) > 1) {
        // Multiple matching users - this should be very rare with enhanced fingerprinting
        $conflicting_users = array_map(function($match) { return $match['user']; }, $matching_users);
        $user_names = implode(', ', array_map(function($u) { return $u['full_name']; }, $conflicting_users));
        debug_log("attendance.php - RARE: Multiple users still match enhanced device characteristics: " . $user_names);
        $_SESSION['msg'] = "❌ Device conflict detected. Multiple users (" . $user_names . ") have very similar devices. Please contact admin for device-specific registration.";
    }
    $stmt->close();
}

// Strategy 3: Fallback to phone model (only if no device characteristics match found)
if (!$staff && empty($conflicting_users) && $phone_model) {
    $stmt = $conn->prepare("SELECT * FROM staff WHERE phone_model = ? AND unique_id IS NOT NULL AND unique_id != ''");
    $stmt->bind_param("s", $phone_model);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 1) {
        // Only one user with this phone model - safe to use
        $staff = $result->fetch_assoc();
        $identification_method = 'phone_model';
        debug_log("attendance.php - Found staff by phone model (single match): " . $staff['full_name'] . " (ID: " . $staff['unique_id'] . ")");
    } elseif ($result->num_rows > 1) {
        // Multiple users with same phone model - cannot determine which user
        $user_list = [];
        while ($row = $result->fetch_assoc()) {
            $user_list[] = $row['full_name'];
        }
        debug_log("attendance.php - Multiple users found for phone model " . $phone_model . ": " . implode(', ', $user_list));
        $_SESSION['msg'] = "❌ Multiple users (" . implode(', ', $user_list) . ") use the same device model. Enhanced device fingerprinting failed to distinguish between devices. Please contact admin.";
    } else {
        debug_log("attendance.php - No registered staff found for phone model: " . $phone_model);
    }
    $stmt->close();
}

// If no staff found, provide clear error message with MAC address suggestion
if (!$staff && $phone_model) {
    debug_log("attendance.php - No unique user identification possible for phone model: " . $phone_model);
    $_SESSION['msg'] = "❌ Cannot identify user. Multiple users may be using identical devices. Please register your device MAC address using the 'Upgrade Registration' option for unique identification.";
}


// Handle attendance POST (clock_in / clock_out)
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['action']) && $staff) {
    $action = $_POST['action'];

    // Enhanced security: Verify device identity
    $device_verified = false;

    // If user was found by device token, verification is already done
    if ($device_token && $staff['device_token'] === $device_token) {
        $device_verified = true;
        debug_log("attendance.php - Device verified by token for " . $staff['full_name']);
    }
    // If user was found by device characteristics matching, verify phone model matches
    elseif ($device_characteristics_hash && $staff['device_info']) {
        if ($phone_model === $staff['phone_model']) {
            $device_verified = true;
            debug_log("attendance.php - Device verified by characteristics + phone model for " . $staff['full_name']);
        } else {
            $_SESSION['msg'] = "❌ Phone model mismatch. Access denied.";
            debug_log("attendance.php - Phone model mismatch for " . $staff['full_name'] . ". Expected: " . $staff['phone_model'] . ", Got: " . $phone_model);
            header("Location: " . $_SERVER['REQUEST_URI']);
            exit;
        }
    }
    // If user was found by phone model only, verify it matches
    elseif ($phone_model && $staff['phone_model']) {
        if ($phone_model === $staff['phone_model']) {
            $device_verified = true;
            debug_log("attendance.php - Device verified by phone model for " . $staff['full_name']);
        } else {
            $_SESSION['msg'] = "❌ Phone model mismatch. Access denied.";
            debug_log("attendance.php - Phone model mismatch for " . $staff['full_name'] . ". Expected: " . $staff['phone_model'] . ", Got: " . $phone_model);
            header("Location: " . $_SERVER['REQUEST_URI']);
            exit;
        }
    } else {
        $_SESSION['msg'] = "❌ Device verification failed. Please re-register your device.";
        debug_log("attendance.php - No device verification method available for " . $staff['full_name']);
        header("Location: " . $_SERVER['REQUEST_URI']);
        exit;
    }

    // Check today's attendance record for this staff
    $stmt = $conn->prepare("SELECT * FROM attendance WHERE staff_id = ? AND date = ?");
    $stmt->bind_param("is", $staff['id'], $today);
    $stmt->execute();
    $res = $stmt->get_result();
    $row = $res->fetch_assoc();
    $stmt->close();

    debug_log("attendance.php - Processing $action for " . $staff['full_name'] . " (ID: " . $staff['unique_id'] . ")");

    if ($action === "clock_in") {
        if ($row && $row['clock_in']) {
            $_SESSION['msg'] = "⏰ Already clocked in at " . $row['clock_in'];
            debug_log("attendance.php - " . $staff['full_name'] . " already clocked in at " . $row['clock_in']);
        } elseif ($row) {
            // Update existing record with enhanced device tracking
            $stmt = $conn->prepare("UPDATE attendance SET clock_in = ?, clock_in_phone_model = ?, clock_in_device_fingerprint = ? WHERE id = ?");
            $stmt->bind_param("sssi", $now, $phone_model, $device_characteristics_hash, $row['id']);
            if ($stmt->execute()) {
                $_SESSION['msg'] = "✅ Clocked in successfully at $now";
                debug_log("attendance.php - " . $staff['full_name'] . " (ID: " . $staff['unique_id'] . ") clocked in at $now - UPDATE successful with device characteristics");

                // Force database to commit the transaction
                $conn->commit();
            } else {
                $_SESSION['msg'] = "❌ Database error during clock in. Please try again.";
                debug_log("attendance.php - UPDATE failed for " . $staff['full_name'] . ": " . $conn->error);
            }
            $stmt->close();
        } else {
            // Insert new record with enhanced device tracking
            $stmt = $conn->prepare("INSERT INTO attendance (staff_id, date, clock_in, clock_in_phone_model, clock_in_device_fingerprint) VALUES (?, ?, ?, ?, ?)");
            $stmt->bind_param("issss", $staff['id'], $today, $now, $phone_model, $device_characteristics_hash);
            if ($stmt->execute()) {
                $_SESSION['msg'] = "✅ Clocked in successfully at $now";
                debug_log("attendance.php - " . $staff['full_name'] . " (ID: " . $staff['unique_id'] . ") clocked in at $now - INSERT successful with device characteristics");

                // Force database to commit the transaction
                $conn->commit();
            } else {
                $_SESSION['msg'] = "❌ Database error during clock in. Please try again.";
                debug_log("attendance.php - INSERT failed for " . $staff['full_name'] . ": " . $conn->error);
            }
            $stmt->close();
        }
    } elseif ($action === "clock_out") {
        if (!$row || !$row['clock_in']) {
            $_SESSION['msg'] = "⚠️ You must clock in first before you can clock out.";
            debug_log("attendance.php - " . $staff['full_name'] . " tried to clock out without clocking in");
        } elseif ($row['clock_out']) {
            $_SESSION['msg'] = "⏰ Already clocked out at " . $row['clock_out'];
            debug_log("attendance.php - " . $staff['full_name'] . " already clocked out at " . $row['clock_out']);
        } else {
            // Update with clock out time and enhanced device tracking
            $stmt = $conn->prepare("UPDATE attendance SET clock_out = ?, clock_out_phone_model = ?, clock_out_device_fingerprint = ? WHERE id = ?");
            $stmt->bind_param("sssi", $now, $phone_model, $device_characteristics_hash, $row['id']);
            if ($stmt->execute()) {
                $_SESSION['msg'] = "✅ Clocked out successfully at $now";
                debug_log("attendance.php - " . $staff['full_name'] . " (ID: " . $staff['unique_id'] . ") clocked out at $now - UPDATE successful with device characteristics");

                // Force database to commit the transaction
                $conn->commit();
            } else {
                $_SESSION['msg'] = "❌ Database error during clock out. Please try again.";
                debug_log("attendance.php - Clock out UPDATE failed for " . $staff['full_name'] . ": " . $conn->error);
            }
            $stmt->close();
        }
    }

    // Simple redirect to prevent form resubmission
    header("Location: attendance.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Attendance - Staff Attendance System</title>
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 500px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            position: relative;
        }
        .admin-login-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .admin-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .message {
            padding: 20px;
            margin: 25px 0;
            border-radius: 10px;
            font-weight: 500;
            line-height: 1.5;
        }
        .success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-left: 4px solid #28a745;
        }
        .error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        .info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }
        .warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border-left: 4px solid #ffc107;
        }
        .user-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 6px solid #2196f3;
        }
        .user-info h3 {
            margin: 0 0 15px 0;
            color: #1976d2;
        }
        .user-detail {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
        }
        .user-detail strong {
            color: #333;
        }
        .unique-id {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            color: #7b1fa2;
            font-family: monospace;
            font-size: 1.1rem;
            font-weight: 600;
            letter-spacing: 1px;
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #9c27b0;
        }
        .attendance-buttons {
            text-align: center;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
        }
        .btn {
            font-size: 1.1rem;
            padding: 18px 35px;
            margin: 10px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            min-width: 160px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .phone-detection {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 6px solid #ffc107;
            text-align: center;
        }
        .attendance-status {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            border: 2px solid #dee2e6;
        }
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.2s ease;
        }
        .status-card:hover {
            transform: translateY(-2px);
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }



        /* Mobile Responsive */
        @media (max-width: 768px) {
            body {
                padding: 5px;
            }
            .container {
                margin: 20px auto;
            }
            .header {
                padding: 20px 15px;
            }
            .header h1 {
                font-size: 1.5rem;
            }
            .content {
                padding: 20px 15px;
            }
            .btn {
                font-size: 1rem;
                padding: 15px 25px;
                min-width: 140px;
            }
            .admin-login-btn {
                position: static;
                display: block;
                text-align: center;
                margin: 0 auto 20px auto;
                max-width: 120px;
            }
            .status-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            .status-card {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if (!$is_admin): ?>
        <a href="admin_login.php" class="admin-login-btn">👨‍💼 Admin</a>
        <?php endif; ?>

        <div class="header">
            <h1>📋 Daily Attendance</h1>
            <p>Clock in/out for your daily work attendance</p>
        </div>

        <div class="content">
            <?php if (isset($_SESSION['msg'])): ?>
                <div class="message <?= strpos($_SESSION['msg'], '✅') !== false ? 'success' : (strpos($_SESSION['msg'], '⚠️') !== false ? 'warning' : (strpos($_SESSION['msg'], '⏰') !== false ? 'info' : 'error')) ?>">
                    <?= $_SESSION['msg'] ?>
                </div>
                <?php
                // If it's a success message, add a meta refresh to update the page
                if (strpos($_SESSION['msg'], '✅') !== false) {
                    echo '<meta http-equiv="refresh" content="2">';
                }
                unset($_SESSION['msg']);
                ?>
            <?php endif; ?>

            <?php if ($staff): ?>
                <!-- Registered User - Show Clock In/Out Interface -->
                <div class="user-info">
                    <h3>👤 Welcome, <?= htmlspecialchars($staff['full_name']) ?>!</h3>

                    <div class="user-detail">
                        <strong>👤 Full Name:</strong> <?= htmlspecialchars($staff['full_name']) ?>
                    </div>
                    <div class="user-detail">
                        <strong>🆔 EID/CID/Permit:</strong> <?= htmlspecialchars($staff['eid_cid_permit']) ?>
                    </div>
                    <div class="user-detail">
                        <strong>🏢 Position:</strong> <?= htmlspecialchars($staff['position_title']) ?>
                    </div>
                    <div class="user-detail">
                        <strong>🏛️ Division:</strong> <?= htmlspecialchars($staff['division']) ?>
                    </div>

                    <div class="unique-id">
                        🔐 Unique ID: <?= htmlspecialchars($staff['unique_id']) ?>
                    </div>

                    <?php if ($identification_method): ?>
                    <div class="identification-method" style="margin-top: 10px; padding: 8px; background-color: #e8f5e8; border-radius: 5px; font-size: 12px; color: #2e7d32;">
                        🔍 Identified by:
                        <?php
                        switch($identification_method) {
                            case 'device_token':
                                echo '🔑 Device Token - Secure';
                                break;
                            case 'device_characteristics':
                                echo '📱 Device Characteristics - Moderate';
                                break;
                            case 'phone_model':
                                echo '📞 Phone Model - Basic';
                                break;

                            default:
                                echo 'Unknown method';
                        }
                        ?>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="attendance-buttons">
                    <form method="POST" id="attendanceForm">
                        <input type="hidden" name="phone_model" id="phone_model" value="<?= htmlspecialchars($phone_model) ?>">
                        <input type="hidden" name="device_info" id="device_info" value="">
                        <input type="hidden" name="device_token" id="device_token" value="<?= htmlspecialchars($device_token ?? '') ?>">
                        <button type="submit" name="action" value="clock_in" class="btn btn-success">
                            🕐 Clock In
                        </button>
                        <button type="submit" name="action" value="clock_out" class="btn btn-danger">
                            🕐 Clock Out
                        </button>
                    </form>
                </div>

                <!-- Today's Attendance Status - Enhanced Display -->
                <?php
                // Force fresh database connection and clear any query cache
                $conn->query("SELECT 1"); // Dummy query to ensure connection is fresh

                // Get the most recent attendance record for today with explicit ordering
                $stmt = $conn->prepare("SELECT * FROM attendance WHERE staff_id = ? AND date = ? ORDER BY id DESC, clock_in DESC LIMIT 1");
                $stmt->bind_param("is", $staff['id'], $today);
                $stmt->execute();
                $result = $stmt->get_result();
                $attendance_today = $result->fetch_assoc();
                $stmt->close();

                // Debug logging to see what we're getting
                debug_log("attendance.php - Fetching attendance for staff_id: " . $staff['id'] . ", date: " . $today . " at " . date('H:i:s'));
                if ($attendance_today) {
                    debug_log("attendance.php - Found attendance record ID: " . $attendance_today['id'] . ", clock_in=" . ($attendance_today['clock_in'] ?? 'NULL') . ", clock_out=" . ($attendance_today['clock_out'] ?? 'NULL'));
                } else {
                    debug_log("attendance.php - No attendance record found for today");
                }

                // Also check if there are multiple records (debugging)
                $count_stmt = $conn->prepare("SELECT COUNT(*) as count FROM attendance WHERE staff_id = ? AND date = ?");
                $count_stmt->bind_param("is", $staff['id'], $today);
                $count_stmt->execute();
                $count_result = $count_stmt->get_result()->fetch_assoc();
                $count_stmt->close();
                debug_log("attendance.php - Total attendance records for today: " . $count_result['count']);
                ?>

                <div class="attendance-status">
                    <h4 style="margin: 0 0 20px 0; color: #333; text-align: center; font-size: 1.2rem;">📅 Today's Attendance Status</h4>
                    <div style="text-align: center; color: #666; margin-bottom: 20px; font-size: 0.95rem;">
                        <?= date('l, F j, Y') ?> • Current Time: <span id="current-time"><?= date('H:i:s') ?></span>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0;">
                        <!-- Clock In Status -->
                        <div style="background: <?= $attendance_today && $attendance_today['clock_in'] ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' : 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)' ?>; padding: 20px; border-radius: 12px; text-align: center; border-left: 4px solid <?= $attendance_today && $attendance_today['clock_in'] ? '#28a745' : '#dc3545' ?>;">
                            <div style="font-size: 1.5rem; margin-bottom: 8px;">
                                <?= $attendance_today && $attendance_today['clock_in'] ? '✅' : '❌' ?>
                            </div>
                            <div style="font-weight: 600; color: <?= $attendance_today && $attendance_today['clock_in'] ? '#155724' : '#721c24' ?>; margin-bottom: 5px;">
                                Clock In
                            </div>
                            <div style="font-size: 0.9rem; color: <?= $attendance_today && $attendance_today['clock_in'] ? '#155724' : '#721c24' ?>;">
                                <?php if ($attendance_today && $attendance_today['clock_in']): ?>
                                    <strong><?= $attendance_today['clock_in'] ?></strong>
                                <?php else: ?>
                                    Not clocked in
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Clock Out Status -->
                        <div style="background: <?= $attendance_today && $attendance_today['clock_out'] ? 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)' : 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)' ?>; padding: 20px; border-radius: 12px; text-align: center; border-left: 4px solid <?= $attendance_today && $attendance_today['clock_out'] ? '#dc3545' : '#ffc107' ?>;">
                            <div style="font-size: 1.5rem; margin-bottom: 8px;">
                                <?= $attendance_today && $attendance_today['clock_out'] ? '🔴' : '⏳' ?>
                            </div>
                            <div style="font-weight: 600; color: <?= $attendance_today && $attendance_today['clock_out'] ? '#721c24' : '#856404' ?>; margin-bottom: 5px;">
                                Clock Out
                            </div>
                            <div style="font-size: 0.9rem; color: <?= $attendance_today && $attendance_today['clock_out'] ? '#721c24' : '#856404' ?>;">
                                <?php if ($attendance_today && $attendance_today['clock_out']): ?>
                                    <strong><?= $attendance_today['clock_out'] ?></strong>
                                <?php elseif ($attendance_today && $attendance_today['clock_in']): ?>
                                    Still working
                                <?php else: ?>
                                    Not available
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Work Duration Calculation -->
                    <?php if ($attendance_today && $attendance_today['clock_in']): ?>
                    <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); padding: 15px; border-radius: 10px; text-align: center; margin-top: 15px; border-left: 4px solid #2196f3;">
                        <div style="color: #1976d2; font-weight: 600; margin-bottom: 5px;">⏱️ Work Duration</div>
                        <div style="color: #1976d2; font-size: 1.1rem; font-weight: 600;" id="work-duration">
                            <?php
                            if ($attendance_today['clock_out']) {
                                // Calculate completed work duration
                                $clock_in_time = new DateTime($today . ' ' . $attendance_today['clock_in']);
                                $clock_out_time = new DateTime($today . ' ' . $attendance_today['clock_out']);
                                $duration = $clock_in_time->diff($clock_out_time);
                                echo $duration->format('%H hours %I minutes');
                            } else {
                                // Calculate current work duration (will be updated by JavaScript)
                                echo "Calculating...";
                            }
                            ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

            <?php else: ?>
                <!-- Phone Detection for Unregistered Users -->
                <div class="phone-detection">
                    <div class="loading"></div>
                    <strong>📱 Detecting your device...</strong>
                    <p>Please wait while we identify your phone and check your registration status.</p>
                    <?php if ($phone_model): ?>
                        <p><strong>Detected Device:</strong> <?= htmlspecialchars($phone_model) ?></p>
                        <div class="message error">
                            ❌ <strong>Device not registered!</strong><br>
                            Your phone model is not registered in the system. Please contact your administrator to register your device first.
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <div style="text-align: center; margin-top: 30px; color: #666;">
                <p>Attendance System v2.0 - Enhanced with Unique ID Support</p>
                <p>Current Time: <?= date('Y-m-d H:i:s') ?></p>
            </div>
        </div>
    </div>

    <script>
    // Auto-update current time
    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    // Update work duration for active sessions
    function updateWorkDuration() {
        const durationElement = document.getElementById('work-duration');
        if (durationElement && durationElement.textContent === 'Calculating...') {
            <?php if ($staff && $attendance_today && $attendance_today['clock_in'] && !$attendance_today['clock_out']): ?>
            // Calculate current work duration
            const clockInTime = new Date('<?= $today ?>T<?= $attendance_today['clock_in'] ?>');
            const now = new Date();
            const diffMs = now - clockInTime;

            const hours = Math.floor(diffMs / (1000 * 60 * 60));
            const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

            durationElement.textContent = `${hours} hours ${minutes} minutes (ongoing)`;
            <?php endif; ?>
        }
    }

    // Update every second
    setInterval(() => {
        updateCurrentTime();
        updateWorkDuration();
    }, 1000);

    // Initial update
    updateCurrentTime();
    updateWorkDuration();

    // Helper functions for device token management
    function getCookie(name) {
        const value = "; " + document.cookie;
        const parts = value.split("; " + name + "=");
        if (parts.length === 2) return parts.pop().split(";").shift();
        return null;
    }

    function generateDeviceToken() {
        return 'dt_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Enhanced device detection and fingerprinting
    (function() {
        // Collect comprehensive device information
        function collectDeviceInfo() {
            const ua = navigator.userAgent;

            // Basic phone model detection
            let phoneModel;
            if (/iPhone|iPad|iPod/.test(ua)) {
                const versionMatch = ua.match(/OS (\d+_\d+)/);
                const version = versionMatch ? versionMatch[1].replace("_", ".") : "unknown";
                phoneModel = "iPhone iOS " + version;
            } else if (/Android/.test(ua)) {
                const androidMatch = ua.match(/Android ([0-9\.]+)/);
                const version = androidMatch ? androidMatch[1] : "unknown";
                phoneModel = "Android " + version;
            } else {
                const match = ua.match(/\(([^)]+)\)/);
                if (match) {
                    const parts = match[1].split(";");
                    phoneModel = parts.slice(1).join(";").trim();
                } else {
                    phoneModel = "UnknownPhone";
                }
            }
            phoneModel += ` (${screen.width}x${screen.height})`;

            // Enhanced device information for unique fingerprinting
            const deviceInfo = {
                userAgent: ua,
                screenResolution: `${screen.width}x${screen.height}`,
                colorDepth: screen.colorDepth || 'unknown',
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'unknown',
                language: navigator.language || navigator.userLanguage || 'unknown',
                platform: navigator.platform || 'unknown',
                hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
                deviceMemory: navigator.deviceMemory || 'unknown',
                touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0 ? 'yes' : 'no',
                cookieEnabled: navigator.cookieEnabled ? 'yes' : 'no',
                doNotTrack: navigator.doNotTrack || 'unknown',
                onLine: navigator.onLine ? 'yes' : 'no',
                // Additional unique characteristics
                availableScreenResolution: `${screen.availWidth}x${screen.availHeight}`,
                pixelRatio: window.devicePixelRatio || 'unknown',
                maxTouchPoints: navigator.maxTouchPoints || 'unknown',
                webdriver: navigator.webdriver ? 'yes' : 'no',
                languages: navigator.languages ? navigator.languages.join(',') : 'unknown',
                vendor: navigator.vendor || 'unknown',
                vendorSub: navigator.vendorSub || 'unknown',
                productSub: navigator.productSub || 'unknown',
                appName: navigator.appName || 'unknown',
                appVersion: navigator.appVersion || 'unknown',
                buildID: navigator.buildID || 'unknown',
                oscpu: navigator.oscpu || 'unknown',
                // Enhanced Canvas fingerprinting for maximum uniqueness
                canvasFingerprint: (function() {
                    try {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        canvas.width = 200;
                        canvas.height = 50;

                        // Multiple drawing operations for unique fingerprint
                        ctx.textBaseline = 'top';
                        ctx.font = '14px Arial';
                        ctx.fillStyle = '#f60';
                        ctx.fillRect(125, 1, 62, 20);
                        ctx.fillStyle = '#069';
                        ctx.fillText('Device fingerprint test 🔒📱', 2, 15);
                        ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
                        ctx.fillText('Unique ID: ' + Date.now(), 4, 35);

                        // Add gradient for more uniqueness
                        const gradient = ctx.createLinearGradient(0, 0, 200, 0);
                        gradient.addColorStop(0, 'red');
                        gradient.addColorStop(1, 'blue');
                        ctx.fillStyle = gradient;
                        ctx.fillRect(0, 0, 200, 10);

                        return canvas.toDataURL();
                    } catch (e) {
                        return 'canvas_error';
                    }
                })(),
                // WebGL fingerprinting
                webglFingerprint: (function() {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                        if (!gl) return 'no_webgl';
                        const renderer = gl.getParameter(gl.RENDERER);
                        const vendor = gl.getParameter(gl.VENDOR);
                        return `${vendor}_${renderer}`.slice(0, 100);
                    } catch (e) {
                        return 'webgl_error';
                    }
                })(),
                // Audio context fingerprinting
                audioFingerprint: (function() {
                    try {
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const oscillator = audioContext.createOscillator();
                        const analyser = audioContext.createAnalyser();
                        const gainNode = audioContext.createGain();
                        oscillator.connect(analyser);
                        analyser.connect(gainNode);
                        gainNode.connect(audioContext.destination);
                        oscillator.frequency.value = 1000;
                        const sampleRate = audioContext.sampleRate;
                        const maxChannelCount = audioContext.destination.maxChannelCount;
                        audioContext.close();
                        return `${sampleRate}_${maxChannelCount}`;
                    } catch (e) {
                        return 'audio_error';
                    }
                })(),
                // Enhanced unique identifiers that work in incognito
                // Font detection fingerprint
                fontFingerprint: (function() {
                    const testFonts = ['Arial', 'Helvetica', 'Times', 'Courier', 'Verdana', 'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS', 'Trebuchet MS', 'Arial Black', 'Impact'];
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const baseFonts = ['monospace', 'sans-serif', 'serif'];
                    const testString = 'mmmmmmmmmmlli';
                    let fingerprint = '';

                    for (let font of testFonts) {
                        for (let baseFont of baseFonts) {
                            ctx.font = '72px ' + font + ',' + baseFont;
                            const width = ctx.measureText(testString).width;
                            fingerprint += width + ',';
                        }
                    }
                    return fingerprint.slice(0, 100);
                })(),
                // CPU benchmark fingerprint
                cpuFingerprint: (function() {
                    const start = performance.now();
                    let result = 0;
                    for (let i = 0; i < 100000; i++) {
                        result += Math.random() * Math.sin(i);
                    }
                    const duration = performance.now() - start;
                    return Math.round(duration * 1000).toString();
                })(),
                // Memory info if available
                memoryInfo: navigator.deviceMemory ? navigator.deviceMemory + 'GB' : 'unknown',
                // GPU info from WebGL
                gpuInfo: (function() {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                        if (!gl) return 'no_webgl';
                        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                        if (debugInfo) {
                            const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                            const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                            return `${vendor}_${renderer}`.slice(0, 50);
                        }
                        return 'no_debug_info';
                    } catch (e) {
                        return 'gpu_error';
                    }
                })(),
                // Screen details
                screenDetails: `${screen.width}x${screen.height}_${screen.colorDepth}bit_${window.devicePixelRatio}dpr`,
                // Battery API if available
                batteryLevel: 'unknown',
                // Screen orientation
                screenOrientation: screen.orientation ? screen.orientation.type : 'unknown',
                // Connection info
                connectionType: navigator.connection ? navigator.connection.effectiveType : 'unknown',
                // Timezone offset (can vary between users)
                timezoneOffset: new Date().getTimezoneOffset(),
                // Timestamp for this specific scan
                scanTimestamp: Date.now()
            };

            return { phoneModel, deviceInfo };
        }

        const { phoneModel, deviceInfo } = collectDeviceInfo();

        <?php if ($staff): ?>
        // For registered users: Update form fields for attendance tracking
        const phoneModelField = document.getElementById('phone_model');
        const deviceInfoField = document.getElementById('device_info');
        const deviceTokenField = document.getElementById('device_token');

        if (phoneModelField) {
            phoneModelField.value = phoneModel;
        }

        if (deviceInfoField) {
            deviceInfoField.value = JSON.stringify(deviceInfo);
        }

        if (deviceTokenField) {
            // Get device token from cookie or generate new one
            let deviceToken = getCookie('device_token');
            if (!deviceToken) {
                deviceToken = generateDeviceToken();
                document.cookie = "device_token=" + deviceToken + "; path=/; max-age=86400";
            }
            deviceTokenField.value = deviceToken;
        }

        // Set device token cookie for future identification
        <?php if ($staff['device_token']): ?>
        document.cookie = "device_token=<?= htmlspecialchars($staff['device_token']) ?>; path=/; max-age=86400";
        <?php endif; ?>
        <?php else: ?>
        // For unregistered users: Submit device info to check registration
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '';

        const pmInput = document.createElement('input');
        pmInput.type = 'hidden';
        pmInput.name = 'phone_model';
        pmInput.value = phoneModel;
        form.appendChild(pmInput);

        const deviceInfoInput = document.createElement('input');
        deviceInfoInput.type = 'hidden';
        deviceInfoInput.name = 'device_info';
        deviceInfoInput.value = JSON.stringify(deviceInfo);
        form.appendChild(deviceInfoInput);

        const macAddressInput = document.createElement('input');
        macAddressInput.type = 'hidden';
        macAddressInput.name = 'mac_address';
        macAddressInput.value = ''; // Empty for unregistered users
        form.appendChild(macAddressInput);

        document.body.appendChild(form);
        form.submit();
        <?php endif; ?>
    })();
    </script>
</body>
</html>
