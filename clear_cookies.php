<?php
// Clear problematic cookies that might cause user conflicts
session_start();

// Clear phone_model cookie (causes user conflicts on shared devices)
if (isset($_COOKIE['phone_model'])) {
    setcookie('phone_model', '', time() - 3600, '/');
    $cleared_phone_model = true;
} else {
    $cleared_phone_model = false;
}

// Clear device_fingerprint cookie (old system, no longer used)
if (isset($_COOKIE['device_fingerprint'])) {
    setcookie('device_fingerprint', '', time() - 3600, '/');
    $cleared_fingerprint = true;
} else {
    $cleared_fingerprint = false;
}

// Keep device_token cookie (this is user-specific and safe)
$has_device_token = isset($_COOKIE['device_token']);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Cookies - Attendance System</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        .header {
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍪 Cookie Cleanup</h1>
            <p>Clearing problematic cookies that might cause user conflicts</p>
        </div>

        <?php if ($cleared_phone_model): ?>
            <div class="status success">
                ✅ Cleared phone_model cookie (was causing user conflicts)
            </div>
        <?php else: ?>
            <div class="status info">
                ℹ️ No phone_model cookie found to clear
            </div>
        <?php endif; ?>

        <?php if ($cleared_fingerprint): ?>
            <div class="status success">
                ✅ Cleared old device_fingerprint cookie
            </div>
        <?php else: ?>
            <div class="status info">
                ℹ️ No device_fingerprint cookie found to clear
            </div>
        <?php endif; ?>

        <?php if ($has_device_token): ?>
            <div class="status info">
                ✅ Device token cookie preserved (this is user-specific and safe)
            </div>
        <?php else: ?>
            <div class="status warning">
                ⚠️ No device token found - you may need to re-register your device
            </div>
        <?php endif; ?>

        <div style="margin-top: 30px;">
            <a href="attendance.php" class="btn">📋 Go to Attendance</a>
            <a href="register_phone.php" class="btn">📱 Register Device</a>
        </div>

        <div style="margin-top: 20px; font-size: 14px; color: #666;">
            <p><strong>What was fixed:</strong></p>
            <p>• Removed phone_model cookies that caused user conflicts on shared devices</p>
            <p>• Device identification now uses fresh device detection every time</p>
            <p>• Device tokens remain for secure user identification</p>
        </div>
    </div>
</body>
</html>
