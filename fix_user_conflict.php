<?php
session_start();
require_once('db.php');
require_once('network_security.php');
date_default_timezone_set('Asia/Thimphu');

// Check network access before proceeding
blockUnauthorizedAccess();

// Debug logging function
function debug_log($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents('debug_log.txt', "[$timestamp] $message\n", FILE_APPEND);
}

$message = '';
$users_with_conflicts = [];

// Check for users with identical device characteristics
$stmt = $conn->prepare("
    SELECT s1.id, s1.full_name, s1.phone_model, s1.device_info, s1.unique_id,
           COUNT(*) as conflict_count
    FROM staff s1 
    JOIN staff s2 ON s1.device_info = s2.device_info 
    WHERE s1.device_info IS NOT NULL 
    AND s1.device_info != '' 
    AND s1.unique_id IS NOT NULL 
    AND s1.unique_id != ''
    AND s2.unique_id IS NOT NULL 
    AND s2.unique_id != ''
    GROUP BY s1.device_info 
    HAVING COUNT(*) > 1
    ORDER BY s1.full_name
");
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $users_with_conflicts[] = $row;
}

// Handle POST request to clear device data for a specific user
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['clear_user_id'])) {
    $user_id = intval($_POST['clear_user_id']);
    
    // Clear device-specific data for this user
    $stmt = $conn->prepare("UPDATE staff SET device_token = NULL, device_fingerprint = NULL, device_info = NULL WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    
    if ($stmt->execute()) {
        debug_log("fix_user_conflict.php - Cleared device data for user ID: " . $user_id);
        $message = "✅ Device data cleared successfully. User must re-register their device.";
        
        // Refresh the conflicts list
        header("Location: " . $_SERVER['REQUEST_URI']);
        exit;
    } else {
        $message = "❌ Error clearing device data: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix User Conflicts - Attendance System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .conflict-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .conflict-item.critical {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .user-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        .user-detail {
            padding: 8px 12px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .btn {
            background: #dc3545;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #c82333;
        }
        .btn-safe {
            background: #28a745;
        }
        .btn-safe:hover {
            background: #218838;
        }
        .message {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .device-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
            max-height: 100px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Fix User Conflicts</h1>
            <p>Resolve device identification conflicts in the attendance system</p>
        </div>

        <?php if ($message): ?>
            <div class="message <?= strpos($message, '✅') !== false ? 'success' : 'error' ?>">
                <?= $message ?>
            </div>
        <?php endif; ?>

        <?php if (empty($users_with_conflicts)): ?>
            <div class="message success">
                ✅ No device conflicts detected! All users have unique device characteristics.
            </div>
        <?php else: ?>
            <div class="message error">
                ⚠️ Found <?= count($users_with_conflicts) ?> users with identical device characteristics. This causes the attendance system to incorrectly identify users.
            </div>

            <?php
            // Group users by their device_info to show conflicts together
            $conflicts_by_device = [];
            foreach ($users_with_conflicts as $user) {
                $device_hash = md5($user['device_info']);
                if (!isset($conflicts_by_device[$device_hash])) {
                    $conflicts_by_device[$device_hash] = [];
                }
                $conflicts_by_device[$device_hash][] = $user;
            }
            ?>

            <?php foreach ($conflicts_by_device as $device_hash => $conflicting_users): ?>
                <div class="conflict-item critical">
                    <h3>🚨 Device Conflict Group</h3>
                    <p><strong>Issue:</strong> The following users have identical device characteristics:</p>
                    
                    <?php foreach ($conflicting_users as $user): ?>
                        <div class="user-info">
                            <div class="user-detail">
                                <strong>Name:</strong> <?= htmlspecialchars($user['full_name']) ?>
                            </div>
                            <div class="user-detail">
                                <strong>Unique ID:</strong> <?= htmlspecialchars($user['unique_id']) ?>
                            </div>
                            <div class="user-detail">
                                <strong>Phone Model:</strong> <?= htmlspecialchars($user['phone_model']) ?>
                            </div>
                            <div class="user-detail">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="clear_user_id" value="<?= $user['id'] ?>">
                                    <button type="submit" class="btn" onclick="return confirm('Clear device data for <?= htmlspecialchars($user['full_name']) ?>? They will need to re-register.')">
                                        Clear Device Data
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <div class="device-info">
                            <strong>Device Info:</strong><br>
                            <?= htmlspecialchars($user['device_info']) ?>
                        </div>
                    <?php endforeach; ?>
                    
                    <p><strong>Solution:</strong> Clear device data for all but one user, then have them re-register with their individual devices.</p>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <div style="margin-top: 30px; text-align: center;">
            <a href="attendance.php" class="btn btn-safe">📋 Back to Attendance</a>
            <a href="admin_dashboard.php" class="btn btn-safe">🏠 Admin Dashboard</a>
        </div>

        <div style="margin-top: 20px; font-size: 14px; color: #666;">
            <p><strong>How this works:</strong></p>
            <p>• The system identifies users by device characteristics (browser, screen size, etc.)</p>
            <p>• When multiple users have identical devices, the system cannot distinguish between them</p>
            <p>• Clearing device data forces users to re-register with fresh, unique identifiers</p>
            <p>• Each user should register from their own personal device to avoid conflicts</p>
        </div>
    </div>
</body>
</html>
