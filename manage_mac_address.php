<?php
session_start();
require_once('db.php');
require_once('network_security.php');
date_default_timezone_set('Asia/Thimphu');

// Check network access before proceeding
blockUnauthorizedAccess();

$message = '';
$error = '';

// Handle form submission for updating MAC address
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $unique_id = trim($_POST['unique_id'] ?? '');
    $mac_address = trim($_POST['mac_address'] ?? '');
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_mac') {
        if (empty($unique_id)) {
            $error = "Please enter your Unique ID.";
        } elseif (empty($mac_address)) {
            $error = "Please enter your device MAC address.";
        } elseif (!preg_match('/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/', $mac_address)) {
            $error = "Please enter a valid MAC address format (e.g., AA:BB:CC:DD:EE:FF or AA-BB-CC-DD-EE-FF).";
        } else {
            // Normalize MAC address format
            $mac_address = strtoupper(str_replace('-', ':', $mac_address));
            
            // Check if user exists
            $stmt = $conn->prepare("SELECT id, full_name, mac_address FROM staff WHERE unique_id = ?");
            $stmt->bind_param("s", $unique_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($row = $result->fetch_assoc()) {
                // Check if MAC address is already in use by another user
                $mac_check_stmt = $conn->prepare("SELECT id, full_name, unique_id FROM staff WHERE mac_address = ? AND unique_id != ?");
                $mac_check_stmt->bind_param("ss", $mac_address, $unique_id);
                $mac_check_stmt->execute();
                $mac_result = $mac_check_stmt->get_result();
                
                if ($mac_result->num_rows > 0) {
                    $existing_mac_user = $mac_result->fetch_assoc();
                    $error = "This MAC address is already registered to " . htmlspecialchars($existing_mac_user['full_name']) . " (ID: " . htmlspecialchars($existing_mac_user['unique_id']) . ").";
                } else {
                    // Update MAC address
                    $update_stmt = $conn->prepare("UPDATE staff SET mac_address = ? WHERE id = ?");
                    $update_stmt->bind_param("si", $mac_address, $row['id']);
                    
                    if ($update_stmt->execute()) {
                        $message = "✅ Successfully updated MAC address for " . htmlspecialchars($row['full_name']) . " to " . htmlspecialchars($mac_address);
                    } else {
                        $error = "Failed to update MAC address. Please try again.";
                    }
                    $update_stmt->close();
                }
                $mac_check_stmt->close();
            } else {
                $error = "Unique ID not found. Please check your ID or contact administrator.";
            }
            $stmt->close();
        }
    } elseif ($action === 'remove_mac') {
        if (empty($unique_id)) {
            $error = "Please enter your Unique ID.";
        } else {
            // Check if user exists
            $stmt = $conn->prepare("SELECT id, full_name, mac_address FROM staff WHERE unique_id = ?");
            $stmt->bind_param("s", $unique_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($row = $result->fetch_assoc()) {
                // Remove MAC address
                $update_stmt = $conn->prepare("UPDATE staff SET mac_address = NULL WHERE id = ?");
                $update_stmt->bind_param("i", $row['id']);
                
                if ($update_stmt->execute()) {
                    $message = "✅ Successfully removed MAC address for " . htmlspecialchars($row['full_name']);
                } else {
                    $error = "Failed to remove MAC address. Please try again.";
                }
                $update_stmt->close();
            } else {
                $error = "Unique ID not found. Please check your ID or contact administrator.";
            }
            $stmt->close();
        }
    }
}

// Get current MAC address registrations
$mac_registrations = [];
$stmt = $conn->prepare("SELECT full_name, unique_id, mac_address, phone_model FROM staff WHERE mac_address IS NOT NULL ORDER BY full_name");
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $mac_registrations[] = $row;
}
$stmt->close();

// Get users without MAC addresses who have device conflicts
$conflict_users = [];
$conflict_sql = "
    SELECT s1.id, s1.full_name, s1.unique_id, s1.phone_model, s1.mac_address,
           COUNT(*) as conflict_count
    FROM staff s1 
    JOIN staff s2 ON s1.phone_model = s2.phone_model 
    WHERE s1.phone_model IS NOT NULL 
    AND s1.phone_model != '' 
    AND s1.unique_id IS NOT NULL 
    AND s1.unique_id != ''
    AND s2.unique_id IS NOT NULL 
    AND s2.unique_id != ''
    AND s1.mac_address IS NULL
    GROUP BY s1.phone_model 
    HAVING COUNT(*) > 1
    ORDER BY s1.phone_model, s1.full_name
";

$conflict_result = $conn->query($conflict_sql);
if ($conflict_result && $conflict_result->num_rows > 0) {
    while ($row = $conflict_result->fetch_assoc()) {
        $conflict_users[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage MAC Address - Attendance System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        
        .message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            margin: 10px 5px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .btn-safe {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .mac-list {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .mac-item {
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .mac-item:last-child {
            border-bottom: none;
        }
        
        .mac-info {
            flex-grow: 1;
        }
        
        .mac-address {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #495057;
        }
        
        .conflict-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #424242;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📶 MAC Address Management</h1>
            <p>Manage device MAC addresses for unique user identification</p>
        </div>

        <?php if ($message): ?>
            <div class="message success">
                <?= $message ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="message error">
                <?= $error ?>
            </div>
        <?php endif; ?>

        <!-- Update/Add MAC Address Form -->
        <div class="form-section">
            <h3>🔧 Update MAC Address</h3>
            <form method="POST">
                <input type="hidden" name="action" value="update_mac">

                <div class="form-group">
                    <label for="unique_id">🔐 Your Unique ID:</label>
                    <input type="text" id="unique_id" name="unique_id" placeholder="Enter your Unique ID (e.g., UID-ABC123-20250620)" required>
                </div>

                <div class="form-group">
                    <label for="mac_address">📶 Device MAC Address:</label>
                    <input type="text" id="mac_address" name="mac_address" placeholder="Enter your device MAC address (e.g., AA:BB:CC:DD:EE:FF)"
                           pattern="^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"
                           title="Please enter a valid MAC address format"
                           required>
                    <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">
                        Use either colon (:) or dash (-) format. Example: AA:BB:CC:DD:EE:FF or AA-BB-CC-DD-EE-FF
                    </small>
                </div>

                <button type="submit" class="btn">📶 Update MAC Address</button>
            </form>
        </div>

        <!-- Remove MAC Address Form -->
        <div class="form-section">
            <h3>🗑️ Remove MAC Address</h3>
            <p style="margin-bottom: 15px; color: #666;">Remove your MAC address registration (not recommended if you have device conflicts)</p>
            <form method="POST">
                <input type="hidden" name="action" value="remove_mac">

                <div class="form-group">
                    <label for="unique_id_remove">🔐 Your Unique ID:</label>
                    <input type="text" id="unique_id_remove" name="unique_id" placeholder="Enter your Unique ID" required>
                </div>

                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to remove your MAC address? This may cause identification conflicts.')">🗑️ Remove MAC Address</button>
            </form>
        </div>

        <!-- Instructions -->
        <div class="instructions">
            <h3>🔍 How to Find Your MAC Address:</h3>
            <div style="margin: 15px 0;">
                <strong>📱 On Android:</strong>
                <ol style="margin-left: 20px; margin-top: 5px;">
                    <li>Go to <strong>Settings</strong> → <strong>About Phone</strong> → <strong>Status</strong></li>
                    <li>Look for <strong>"Wi-Fi MAC address"</strong> or <strong>"MAC address"</strong></li>
                    <li>It looks like: <code>AA:BB:CC:DD:EE:FF</code></li>
                </ol>
            </div>
            <div style="margin: 15px 0;">
                <strong>📱 On iPhone:</strong>
                <ol style="margin-left: 20px; margin-top: 5px;">
                    <li>Go to <strong>Settings</strong> → <strong>General</strong> → <strong>About</strong></li>
                    <li>Look for <strong>"Wi-Fi Address"</strong></li>
                    <li>It looks like: <code>AA:BB:CC:DD:EE:FF</code></li>
                </ol>
            </div>
        </div>

        <!-- Current MAC Address Registrations -->
        <?php if (!empty($mac_registrations)): ?>
        <div style="margin-top: 30px;">
            <h3>📋 Current MAC Address Registrations</h3>
            <div class="mac-list">
                <?php foreach ($mac_registrations as $registration): ?>
                <div class="mac-item">
                    <div class="mac-info">
                        <strong><?= htmlspecialchars($registration['full_name']) ?></strong><br>
                        <small>ID: <?= htmlspecialchars($registration['unique_id']) ?></small><br>
                        <small>Device: <?= htmlspecialchars($registration['phone_model']) ?></small>
                    </div>
                    <div class="mac-address">
                        <?= htmlspecialchars($registration['mac_address']) ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Users with Device Conflicts -->
        <?php if (!empty($conflict_users)): ?>
        <div class="conflict-section">
            <h3>⚠️ Users with Device Conflicts (Need MAC Address Registration)</h3>
            <p>The following users have identical device characteristics and should register their MAC addresses:</p>

            <?php
            $grouped_conflicts = [];
            foreach ($conflict_users as $user) {
                $grouped_conflicts[$user['phone_model']][] = $user;
            }
            ?>

            <?php foreach ($grouped_conflicts as $phone_model => $users): ?>
                <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #ff6b6b;">
                    <strong>Device Type:</strong> <?= htmlspecialchars($phone_model) ?><br>
                    <strong>Conflicting Users:</strong>
                    <ul style="margin-top: 10px;">
                        <?php foreach ($users as $user): ?>
                            <li><?= htmlspecialchars($user['full_name']) ?> (ID: <?= htmlspecialchars($user['unique_id']) ?>)</li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <div style="text-align: center; margin-top: 30px;">
            <a href="upgrade_registration.php" class="btn">🔄 Upgrade Registration</a>
            <a href="attendance.php" class="btn btn-safe">📋 Back to Attendance</a>
            <a href="admin_dashboard.php" class="btn btn-safe">🏠 Admin Dashboard</a>
        </div>

        <div style="margin-top: 20px; font-size: 14px; color: #666; text-align: center;">
            <p><strong>Why MAC Address Registration?</strong></p>
            <p>MAC addresses provide unique device identification, resolving conflicts between users with identical phone models and device characteristics.</p>
        </div>
    </div>
</body>
</html>
