<?php
require_once('db.php');
date_default_timezone_set('Asia/Thimphu');

// Function to check if column exists
function columnExists($conn, $table, $column) {
    $result = $conn->query("SHOW COLUMNS FROM `$table` LIKE '$column'");
    return $result->num_rows > 0;
}

// Function to check if table exists
function tableExists($conn, $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    return $result->num_rows > 0;
}

$messages = [];
$errors = [];

echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Setup - Attendance System</title>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background-color: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .message { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .nav-links { text-align: center; margin-top: 30px; }
        .nav-links a { display: inline-block; margin: 10px; padding: 12px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .nav-links a:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>📊 Database Setup - Enhanced Device Identification</h1>";

// Create staff table if it doesn't exist
if (!tableExists($conn, 'staff')) {
    $sql = "CREATE TABLE staff (
        id INT AUTO_INCREMENT PRIMARY KEY,
        full_name VARCHAR(255) NOT NULL,
        eid_cid_permit VARCHAR(100),
        position_title VARCHAR(255),
        division VARCHAR(255),
        phone_model VARCHAR(500),
        unique_id VARCHAR(100),
        device_token VARCHAR(255),
        device_fingerprint VARCHAR(64),
        device_info TEXT,
        registration_timestamp TIMESTAMP NULL,
        mac_address VARCHAR(17) DEFAULT NULL COMMENT 'Device MAC address for unique identification',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql)) {
        $messages[] = "✅ Created staff table successfully";
    } else {
        $errors[] = "❌ Error creating staff table: " . $conn->error;
    }
} else {
    $messages[] = "ℹ️ Staff table already exists";
}

// Create attendance table if it doesn't exist
if (!tableExists($conn, 'attendance')) {
    $sql = "CREATE TABLE attendance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        staff_id INT NOT NULL,
        date DATE NOT NULL,
        clock_in TIME NULL,
        clock_out TIME NULL,
        clock_in_phone_model VARCHAR(500),
        clock_out_phone_model VARCHAR(500),
        clock_in_device_fingerprint VARCHAR(64),
        clock_out_device_fingerprint VARCHAR(64),
        device_token VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (staff_id) REFERENCES staff(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($sql)) {
        $messages[] = "✅ Created attendance table successfully";
    } else {
        $errors[] = "❌ Error creating attendance table: " . $conn->error;
    }
} else {
    $messages[] = "ℹ️ Attendance table already exists";
}

// Add new columns to staff table if they don't exist
$staff_columns = [
    'device_fingerprint' => 'VARCHAR(64)',
    'device_info' => 'TEXT',
    'registration_timestamp' => 'TIMESTAMP NULL'
];

foreach ($staff_columns as $column => $type) {
    if (!columnExists($conn, 'staff', $column)) {
        $sql = "ALTER TABLE staff ADD COLUMN $column $type";
        if ($conn->query($sql)) {
            $messages[] = "✅ Added column '$column' to staff table";
        } else {
            $errors[] = "❌ Error adding column '$column' to staff table: " . $conn->error;
        }
    } else {
        $messages[] = "ℹ️ Column '$column' already exists in staff table";
    }
}

// Add new columns to attendance table if they don't exist
$attendance_columns = [
    'clock_in_device_fingerprint' => 'VARCHAR(64)',
    'clock_out_device_fingerprint' => 'VARCHAR(64)'
];

foreach ($attendance_columns as $column => $type) {
    if (!columnExists($conn, 'attendance', $column)) {
        $sql = "ALTER TABLE attendance ADD COLUMN $column $type";
        if ($conn->query($sql)) {
            $messages[] = "✅ Added column '$column' to attendance table";
        } else {
            $errors[] = "❌ Error adding column '$column' to attendance table: " . $conn->error;
        }
    } else {
        $messages[] = "ℹ️ Column '$column' already exists in attendance table";
    }
}

// Create indexes for better performance
$indexes = [
    "CREATE INDEX idx_staff_device_fingerprint ON staff(device_fingerprint)" => "staff device_fingerprint index",
    "CREATE INDEX idx_staff_phone_model ON staff(phone_model)" => "staff phone_model index",
    "CREATE INDEX idx_attendance_date ON attendance(date)" => "attendance date index",
    "CREATE INDEX idx_attendance_staff_date ON attendance(staff_id, date)" => "attendance staff_date index"
];

foreach ($indexes as $sql => $description) {
    // Check if index exists by trying to create it and catching the error
    $result = $conn->query($sql);
    if ($result) {
        $messages[] = "✅ Created $description";
    } else {
        // Index might already exist, which is fine
        if (strpos($conn->error, 'Duplicate key name') !== false) {
            $messages[] = "ℹ️ Index $description already exists";
        } else {
            $errors[] = "❌ Error creating $description: " . $conn->error;
        }
    }
}

// Display results
foreach ($messages as $message) {
    echo "<div class='message success'>$message</div>";
}

foreach ($errors as $error) {
    echo "<div class='message error'>$error</div>";
}

if (empty($errors)) {
    echo "<div class='message info'>
        <h3>🎉 Database Setup Complete!</h3>
        <p><strong>Enhanced Device Identification System is now ready!</strong></p>
        <ul>
            <li>✅ Enhanced device fingerprinting for unique identification</li>
            <li>✅ Multiple device characteristics combined for security</li>
            <li>✅ Backward compatibility with existing registrations</li>
            <li>✅ Improved performance with database indexes</li>
        </ul>
        <p><em>You can now proceed with device registration and attendance tracking.</em></p>
    </div>";
} else {
    echo "<div class='message error'>
        <h3>⚠️ Setup completed with some errors</h3>
        <p>Please review the errors above and contact your system administrator if needed.</p>
    </div>";
}

echo "
        <div class='nav-links'>
            <a href='index.php'>🏠 Home</a>
            <a href='register_phone.php'>📱 Register Device</a>
            <a href='admin_login.php'>👨‍💼 Admin Login</a>
        </div>
    </div>
</body>
</html>";

$conn->close();
?>
