<?php
session_start();
require_once('db.php');
require_once('network_security.php');
date_default_timezone_set('Asia/Thimphu');

// Check network access before proceeding
blockUnauthorizedAccess();

// Debug logging function
function debug_log($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents('debug_log.txt', "[$timestamp] $message\n", FILE_APPEND);
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $device_token = $_COOKIE['device_token'] ?? '';
    $device_info_json = $_POST['device_info'] ?? '';
    
    if (empty($device_token)) {
        $error = "No device token found. Please register your device first.";
    } elseif (empty($device_info_json)) {
        $error = "Failed to collect device information. Please try again.";
    } else {
        // Find user by device token
        $stmt = $conn->prepare("SELECT id, full_name, device_info FROM staff WHERE device_token = ?");
        $stmt->bind_param("s", $device_token);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 1) {
            $user = $result->fetch_assoc();
            $current_device_info = json_decode($device_info_json, true);
            $stored_device_info = json_decode($user['device_info'] ?? '{}', true);
            
            // Check if update is needed
            $enhanced_characteristics = ['canvasFingerprint', 'webglFingerprint', 'audioFingerprint', 'availableScreenResolution', 'pixelRatio'];
            $needs_update = false;
            $new_characteristics = [];
            
            foreach ($enhanced_characteristics as $char) {
                if (isset($current_device_info[$char]) && !isset($stored_device_info[$char])) {
                    $needs_update = true;
                    $new_characteristics[] = $char;
                }
            }
            
            if ($needs_update) {
                // Update device info
                $update_stmt = $conn->prepare("UPDATE staff SET device_info = ? WHERE id = ?");
                $update_stmt->bind_param("si", $device_info_json, $user['id']);
                
                if ($update_stmt->execute()) {
                    debug_log("update_device_info.php - Updated device info for " . $user['full_name'] . " with: " . implode(', ', $new_characteristics));
                    $message = "✅ Successfully updated device information for " . htmlspecialchars($user['full_name']) . "!<br>Added: " . implode(', ', $new_characteristics);
                } else {
                    $error = "Failed to update device information. Please try again.";
                }
                $update_stmt->close();
            } else {
                $message = "ℹ️ Your device information is already up to date with enhanced characteristics.";
            }
        } else {
            $error = "Device not found. Please register your device first.";
        }
        $stmt->close();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Device Info - Attendance System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .message {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        .status.collecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.ready {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #007bff;
            text-decoration: none;
        }
        .info-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Update Device Information</h1>
            <p>Enhance your device fingerprint for better identification</p>
        </div>

        <?php if ($message): ?>
            <div class="message <?= strpos($message, '✅') !== false ? 'success' : (strpos($message, 'ℹ️') !== false ? 'info' : 'error') ?>">
                <?= $message ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="message error">
                <?= $error ?>
            </div>
        <?php endif; ?>

        <div class="info-box">
            <h3>📱 What this does:</h3>
            <p>This tool updates your device information with enhanced characteristics that help the system better distinguish between similar devices.</p>
            <p><strong>Enhanced features include:</strong></p>
            <ul>
                <li>Canvas fingerprinting for unique device signatures</li>
                <li>WebGL renderer information</li>
                <li>Audio context characteristics</li>
                <li>Enhanced screen and display properties</li>
                <li>Browser-specific identifiers</li>
            </ul>
        </div>

        <form method="POST" id="updateForm">
            <div id="device-status" class="status collecting">
                🔄 Collecting enhanced device information...
            </div>
            
            <input type="hidden" id="device_info" name="device_info">
            
            <div style="text-align: center;">
                <button type="submit" class="btn" id="submitBtn" disabled>
                    🔄 Update Device Information
                </button>
            </div>
        </form>
        
        <div class="back-link">
            <a href="attendance.php">📋 Back to Attendance</a> |
            <a href="index.php">🏠 Main Page</a>
        </div>
    </div>

    <script>
        // Enhanced device information collection
        function collectDeviceInfo() {
            const ua = navigator.userAgent;
            
            const deviceInfo = {
                userAgent: ua,
                screenResolution: `${screen.width}x${screen.height}`,
                colorDepth: screen.colorDepth || 'unknown',
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'unknown',
                language: navigator.language || navigator.userLanguage || 'unknown',
                platform: navigator.platform || 'unknown',
                hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
                deviceMemory: navigator.deviceMemory || 'unknown',
                touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0 ? 'yes' : 'no',
                cookieEnabled: navigator.cookieEnabled ? 'yes' : 'no',
                doNotTrack: navigator.doNotTrack || 'unknown',
                onLine: navigator.onLine ? 'yes' : 'no',
                // Enhanced characteristics
                availableScreenResolution: `${screen.availWidth}x${screen.availHeight}`,
                pixelRatio: window.devicePixelRatio || 'unknown',
                maxTouchPoints: navigator.maxTouchPoints || 'unknown',
                webdriver: navigator.webdriver ? 'yes' : 'no',
                languages: navigator.languages ? navigator.languages.join(',') : 'unknown',
                vendor: navigator.vendor || 'unknown',
                vendorSub: navigator.vendorSub || 'unknown',
                productSub: navigator.productSub || 'unknown',
                appName: navigator.appName || 'unknown',
                appVersion: navigator.appVersion || 'unknown',
                buildID: navigator.buildID || 'unknown',
                oscpu: navigator.oscpu || 'unknown',
                // Canvas fingerprinting
                canvasFingerprint: (function() {
                    try {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        ctx.textBaseline = 'top';
                        ctx.font = '14px Arial';
                        ctx.fillText('Device fingerprint test 🔒', 2, 2);
                        return canvas.toDataURL().slice(-50);
                    } catch (e) {
                        return 'canvas_error';
                    }
                })(),
                // WebGL fingerprinting
                webglFingerprint: (function() {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                        if (!gl) return 'no_webgl';
                        const renderer = gl.getParameter(gl.RENDERER);
                        const vendor = gl.getParameter(gl.VENDOR);
                        return `${vendor}_${renderer}`.slice(0, 100);
                    } catch (e) {
                        return 'webgl_error';
                    }
                })(),
                // Audio context fingerprinting
                audioFingerprint: (function() {
                    try {
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const oscillator = audioContext.createOscillator();
                        const analyser = audioContext.createAnalyser();
                        const gainNode = audioContext.createGain();
                        oscillator.connect(analyser);
                        analyser.connect(gainNode);
                        gainNode.connect(audioContext.destination);
                        oscillator.frequency.value = 1000;
                        const sampleRate = audioContext.sampleRate;
                        const maxChannelCount = audioContext.destination.maxChannelCount;
                        audioContext.close();
                        return `${sampleRate}_${maxChannelCount}`;
                    } catch (e) {
                        return 'audio_error';
                    }
                })(),
                screenOrientation: screen.orientation ? screen.orientation.type : 'unknown',
                connectionType: navigator.connection ? navigator.connection.effectiveType : 'unknown',
                updateTimestamp: Date.now()
            };
            
            return deviceInfo;
        }
        
        // Initialize device collection
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const deviceInfo = collectDeviceInfo();
                document.getElementById('device_info').value = JSON.stringify(deviceInfo);
                
                const statusDiv = document.getElementById('device-status');
                const submitBtn = document.getElementById('submitBtn');
                
                statusDiv.className = 'status ready';
                statusDiv.innerHTML = '✅ Enhanced device information collected successfully';
                submitBtn.disabled = false;
            }, 1500);
        });
    </script>
</body>
</html>
